{"magic_forge": {"name": "Magic Forge", "type": "production", "sprite": "magic_forge", "spriteIndex": 0, "recipe": {"input": {"magic_ore": 2}, "output": {"arcane_essence": 1}}, "productionInterval": 3000, "cost": {"magic_ore": 20, "enchanted_wood": 10}, "description": "將原始魔法礦石轉化為奧術精華。"}, "hunters_lodge_old": {"name": "猎人小屋", "type": "collector", "sprite": "hunters_lodge", "spriteIndex": 20, "recipe": {"input": {}, "output": {"raw_meat": 1}}, "productionInterval": 3000, "cost": {"wood_plank": 15, "stone": 5}, "description": "猎人在此居住并狩猎野生动物，提供肉类和兽皮资源。", "workerRequirement": {"count": 3, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于狩猎肉类，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "worker"}}, {"id": "mushroom", "name": "采集蘑菇", "description": "猎人在狩猎过程中同时采集森林中的蘑菇。", "resources": {"wild_mushroom": 1}, "timeModifier": 1.1, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "animal_hide", "name": "收集兽皮", "description": "猎人在狩猎过程中收集动物皮毛。", "resources": {"animal_hide": 1}, "timeModifier": 1.1, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "wild_herbs", "name": "采集药草", "description": "猎人在狩猎过程中同时采集森林中的药草。", "resources": {"herb": 0.5}, "timeModifier": 1.2, "workerRequirement": {"count": 5, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "简易陷阱", "description": "使用简易陷阱捕获猎物，产量较低。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}, {"id": "tools", "name": "弓箭狩猎", "description": "使用弓箭进行狩猎，提高效率但需要工具维护。", "timeModifier": 0.8, "inputModifiers": {"tool": 0.05}, "outputModifiers": {"meat": 1.3}, "enableByproducts": true, "workerRequirement": {"count": 2, "type": "worker"}}]}, "farm": {"name": "农场", "type": "collector", "sprite": "farm", "spriteIndex": 21, "recipe": {"input": {"water": 2, "manure": 0.25}, "output": {"wheat": 100}}, "productionInterval": 5000, "cost": {"wood": 20, "stone": 10}, "description": "标准农田，20农民/天产出100份小麦。每季度需1单位粪肥，否则减产20%。", "workerRequirement": {"count": 20, "type": "farmer"}, "dailyWage": {"wheat": 3}, "fertilityRequired": true, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于小麦种植，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 20, "type": "farmer"}}, {"id": "hay", "name": "干草生产", "description": "同时生产干草作为副产品，用于喂养牲畜。", "resources": {"hay": 10}, "timeModifier": 1.1, "workerRequirement": {"count": 22, "type": "farmer"}}], "productionMethods": [{"id": "manual", "name": "人力耕作", "description": "使用简单工具进行耕作，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 20, "type": "farmer"}}, {"id": "ox_plow", "name": "牛犁耕作", "description": "使用耕牛和犁提高效率，每10农民需1头耕牛。", "timeModifier": 0.8, "inputModifiers": {"water": 2, "manure": 0.25}, "outputModifiers": {"wheat": 1.5}, "enableByproducts": true, "workerRequirement": {"count": 15, "type": "farmer"}, "animalRequirement": {"ox": 2}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "intensive", "name": "密集耕作", "description": "密集耕作方式，提高产量但需要更多工人。", "timeModifier": 0.8, "workerModifier": 1.4}]}, "orchard": {"name": "果园", "type": "collector", "sprite": "orchard", "spriteIndex": 22, "recipe": {"input": {"water": 3}, "output": {"banana": 1, "orange": 1, "watermelon": 0.5}}, "productionInterval": 5000, "cost": {"wood_plank": 15, "stone": 5}, "description": "种植各种水果树木，需要充足的水灌溉，提供多样化的水果供应。", "workerRequirement": {"count": 6, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于水果生产，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 6, "type": "worker"}}, {"id": "firewood", "name": "木柴采集", "description": "从果树修剪中获取木柴作为副产品。", "resources": {"firewood": 1}, "timeModifier": 1.1, "workerRequirement": {"count": 7, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "基础种植", "description": "基础的果树种植和管理方法。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 6, "type": "worker"}}, {"id": "tools", "name": "工具辅助", "description": "使用专业园艺工具和灌溉系统提高效率。", "timeModifier": 0.8, "inputModifiers": {"water": 4, "tool": 0.05}, "outputModifiers": {"banana": 1.3, "orange": 1.3, "watermelon": 1.3}, "enableByproducts": true, "workerRequirement": {"count": 4, "type": "worker"}}]}, "bakery_old": {"name": "面包房(旧)", "type": "production", "sprite": "bakery", "spriteIndex": 23, "recipe": {"input": {"grain_flour": 2, "water": 1}, "output": {"bread": 3}}, "productionInterval": 3000, "cost": {"wood_plank": 20, "stone": 15, "red_brick": 10}, "description": "旧版面包房，已被新经济系统替代。"}, "wood_enchanter": {"name": "Wood Enchanter", "type": "production", "sprite": "wood_enchanter", "spriteIndex": 1, "recipe": {"input": {"enchanted_wood": 2}, "output": {"mystic_planks": 1}}, "productionInterval": 4000, "cost": {"enchanted_wood": 25, "arcane_crystal": 5}, "description": "將附魔木材加工成神秘木板。", "workerRequirement": {"count": 10, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "没有副产品", "description": "专注于主要产出，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 10, "type": "worker"}}, {"id": "wood_chips", "name": "碎木为副产品", "description": "加工过程中产生木碎作为副产品，但需要更多工人。", "resources": {"wood_chips": 2}, "timeModifier": 0.9, "workerRequirement": {"count": 11, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "徒手工作", "description": "工人徒手加工木材，正常产出。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 10, "type": "worker"}}, {"id": "tools", "name": "工具辅助", "description": "使用工具加工木材，提高产出但需要额外资源。", "timeModifier": 0.6, "inputModifiers": {"enchanted_wood": 1.0, "tools": 1}, "outputModifiers": {"mystic_planks": 2.5}, "enableByproducts": true, "workerRequirement": {"count": 4, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.55, "workerModifier": 1.6}]}, "crystal_refiner": {"name": "Crystal Refiner", "type": "production", "sprite": "crystal_refiner", "spriteIndex": 2, "recipe": {"input": {"arcane_crystal": 2}, "output": {"refined_crystal": 1}}, "productionInterval": 5000, "cost": {"arcane_crystal": 25, "magic_ore": 15}, "description": "將原始奧術水晶精煉成高純度水晶。"}, "potion_brewery": {"name": "Potion Brewery", "type": "production", "sprite": "potion_brewery", "spriteIndex": 3, "recipe": {"input": {"arcane_essence": 1, "mana": 2}, "output": {"magical_potion": 1}}, "productionInterval": 8000, "cost": {"arcane_essence": 15, "refined_crystal": 10}, "description": "釀造魔法藥水，用於高級魔法研究。"}, "artifact_workshop": {"name": "Artifact Workshop", "type": "production", "sprite": "artifact_workshop", "spriteIndex": 4, "recipe": {"input": {"mystic_planks": 2, "refined_crystal": 1}, "output": {"enchanted_artifact": 1}}, "productionInterval": 10000, "cost": {"mystic_planks": 20, "refined_crystal": 15}, "description": "製作附魔神器，用於高級魔法研究。"}, "arcane_workshop": {"name": "Arcane Workshop", "type": "advanced", "sprite": "arcane_workshop", "spriteIndex": 5, "recipe": {"input": {"enchanted_artifact": 1, "magical_potion": 1}, "output": {"magical_construct": 1}}, "productionInterval": 15000, "cost": {"enchanted_artifact": 10, "magical_potion": 15, "refined_crystal": 20}, "description": "符文熔鑄爐:製作魔法構造體，用於自動化魔法操作。"}, "magic_mine": {"name": "Magic Mine", "type": "collector", "sprite": "magic_mine", "spriteIndex": 6, "recipe": {"input": {}, "output": {"magic_ore": 1}}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "不生产副产品，专注于主要产出。", "resources": {}}, {"id": "arcane_crystal", "name": "奥术水晶", "description": "在开采过程中有小概率发现奥术水晶。", "resources": {"arcane_crystal": 0.2}}, {"id": "stone", "name": "石头", "description": "开采过程中产生大量的普通石头。", "resources": {"stone": 3}}], "productionMethods": [{"id": "manual", "name": "徒手工作", "description": "工人徒手加工木材，正常产出。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 10, "type": "worker"}}, {"id": "tools", "name": "工具辅助", "description": "使用工具加工木材，提高产出但需要额外资源。", "timeModifier": 0.6, "inputModifiers": {"enchanted_wood": 1.0, "tools": 1}, "outputModifiers": {"mystic_planks": 2.5}, "enableByproducts": true, "workerRequirement": {"count": 4, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.55, "workerModifier": 1.6}], "productionInterval": 2000, "cost": {"magic_ore": 10}, "description": "魔晶礦脈入口:開採原始魔法礦石。"}, "enchanted_forest": {"name": "Enchanted Forest", "type": "collector", "sprite": "enchanted_forest", "spriteIndex": 7, "recipe": {"input": {}, "output": {"enchanted_wood": 1}}, "productionInterval": 2000, "cost": {"magic_ore": 15}, "description": "種植並收穫附魔木材。"}, "crystal_mine": {"name": "Crystal Mine", "type": "collector", "sprite": "crystal_mine", "spriteIndex": 8, "recipe": {"input": {}, "output": {"arcane_crystal": 1}}, "productionInterval": 3000, "cost": {"magic_ore": 10, "enchanted_wood": 10}, "description": "從深層地下開採奧術水晶。"}, "mana_well": {"name": "Mana Well", "type": "collector", "sprite": "mana_well", "spriteIndex": 9, "recipe": {"input": {}, "output": {"mana": 1}}, "productionInterval": 1500, "cost": {"magic_ore": 5, "arcane_crystal": 5}, "description": "從地下抽取魔力能量。"}, "wizard_tower": {"name": "Wizard Tower", "type": "special", "sprite": "wizard_tower", "spriteIndex": 10, "recipe": {"input": {"mana": 2}, "output": {"research_point": 1}}, "productionInterval": 10000, "cost": {"arcane_essence": 30, "refined_crystal": 20, "mystic_planks": 25}, "description": "產生研究點數，用於解鎖新技術。"}, "arcane_library": {"name": "Arcane Library", "type": "special", "sprite": "arcane_library", "spriteIndex": 11, "recipe": {"input": {"research_point": 1}, "output": {"knowledge": 1}}, "productionInterval": 15000, "cost": {"mystic_planks": 40, "enchanted_artifact": 5}, "description": "將研究點數轉化為知識，提高所有建築效率。"}, "mana_fountain": {"name": "Mana Fountain", "type": "special", "sprite": "mana_fountain", "spriteIndex": 14, "recipe": {"input": {"arcane_essence": 1, "refined_crystal": 1}, "output": {"mana": 5}}, "productionInterval": 6000, "cost": {"arcane_essence": 25, "refined_crystal": 25}, "description": "將奧術精華和精煉水晶轉化為大量魔力。"}, "magic_academy": {"name": "Magic Academy", "type": "advanced", "sprite": "magic_academy", "spriteIndex": 15, "recipe": {"input": {"knowledge": 1, "mana": 5}, "output": {"wizard": 1}}, "productionInterval": 30000, "cost": {"knowledge": 10, "magical_potion": 20, "enchanted_artifact": 15}, "description": "浮空講堂:訓練法師，提高城市的魔法產出。"}, "research_lab": {"name": "Research Lab", "type": "advanced", "sprite": "research_lab", "spriteIndex": 16, "recipe": {"input": {"knowledge": 2, "magical_potion": 1}, "output": {"research_point": 5}}, "productionInterval": 20000, "cost": {"knowledge": 15, "magical_potion": 25, "refined_crystal": 30}, "description": "進行高級魔法研究，產生大量研究點數。"}, "storage_vault": {"name": "Storage Vault", "type": "utility", "sprite": "storage_vault", "spriteIndex": 17, "recipe": {"input": {}, "output": {}}, "productionInterval": 0, "cost": {"mystic_planks": 30, "refined_crystal": 20, "arcane_essence": 15}, "description": "增加資源存儲上限，防止資源浪費。"}, "housing_district": {"name": "Housing District", "type": "housing", "sprite": "housing_district", "spriteIndex": 12, "recipe": {"input": {}, "output": {}}, "productionInterval": 0, "cost": {"enchanted_wood": 20, "magic_ore": 10}, "housingCapacity": 10, "description": "為普通居民提供住所，增加人口上限。"}, "wizard_quarters": {"name": "Wizard Quarters", "type": "housing", "sprite": "wizard_quarters", "spriteIndex": 13, "recipe": {"input": {}, "output": {}}, "productionInterval": 0, "cost": {"mystic_planks": 30, "refined_crystal": 15, "magical_potion": 5}, "housingCapacity": 5, "specialHousing": "wizard", "description": "為法師提供特別的住所，提高法師的效率。"}, "basic_house": {"name": "平房", "type": "housing", "sprite": "basic_house", "spriteIndex": 18, "recipe": {"input": {}, "output": {}}, "productionInterval": 0, "cost": {"enchanted_wood": 10, "stone": 15}, "housingCapacity": 5, "description": "簡單的平房，提供基本居住空間。性價比高但佔地較大。"}, "lumber_mill": {"name": "伐木场", "type": "production", "sprite": "lumber_mill", "spriteIndex": 24, "recipe": {"input": {"enchanted_wood": 2}, "output": {"wood_plank": 4}}, "productionInterval": 2500, "cost": {"enchanted_wood": 20, "stone": 10}, "description": "将原木加工成木板，是建筑、家具和车轮制作的基础材料。", "workerRequirement": {"count": 5, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于木板生产，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 5, "type": "worker"}}, {"id": "firewood", "name": "木柴副产", "description": "加工过程中产生木柴作为副产品。", "resources": {"firewood": 2}, "timeModifier": 1.1, "workerRequirement": {"count": 6, "type": "worker"}}, {"id": "wooden_wheel", "name": "木轮制作", "description": "同时制作木轮作为副产品。", "resources": {"wooden_wheel": 1}, "timeModifier": 1.2, "workerRequirement": {"count": 7, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "手工锯切", "description": "工人手工锯切木材，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 5, "type": "worker"}}, {"id": "water_powered", "name": "水力锯木", "description": "使用水力驱动锯木机，大幅提高效率。", "timeModifier": 0.6, "outputModifiers": {"wood_plank": 1.5}, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.6, "workerModifier": 1.5}]}, "blacksmith": {"name": "铁匠铺", "type": "production", "sprite": "blacksmith", "spriteIndex": 25, "recipe": {"input": {"iron_ore": 2, "firewood": 1}, "output": {"iron_ingot": 1}}, "productionInterval": 4000, "cost": {"wood_plank": 25, "stone": 20, "red_brick": 15}, "description": "冶炼金属并制作各种工具和武器，是城市工业发展的基础。", "workerRequirement": {"count": 4, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于金属冶炼，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "tools", "name": "工具制作", "description": "同时制作基础工具作为副产品。", "resources": {"tool": 0.5}, "timeModifier": 1.2, "workerRequirement": {"count": 5, "type": "worker"}}, {"id": "weapons", "name": "武器制作", "description": "同时制作基础武器作为副产品。", "resources": {"iron_sword": 0.2}, "timeModifier": 1.3, "workerRequirement": {"count": 6, "type": "worker"}}, {"id": "armor", "name": "盔甲制作", "description": "同时制作基础盔甲作为副产品。", "resources": {"iron_shield": 0.2}, "timeModifier": 1.3, "workerRequirement": {"count": 6, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "传统锻造", "description": "使用传统锻造方法，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "improved_furnace", "name": "改良熔炉", "description": "使用改良熔炉提高冶炼效率，但需要更多燃料。", "timeModifier": 0.7, "inputModifiers": {"iron_ore": 3, "firewood": 2, "charcoal": 1}, "outputModifiers": {"iron_ingot": 1.8}, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}]}, "silver_mine": {"name": "银矿", "type": "collector", "sprite": "silver_mine", "spriteIndex": 26, "recipe": {"input": {}, "output": {"silver_ingot": 0.5}}, "productionInterval": 6000, "cost": {"wood_plank": 30, "stone": 25, "iron_ingot": 10}, "description": "开采稀有的银矿石并冶炼成银块，是贵重物品的原料。", "workerRequirement": {"count": 8, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于银矿开采，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 8, "type": "worker"}}, {"id": "stone", "name": "石料采集", "description": "开采过程中收集石料作为副产品。", "resources": {"stone": 2}, "timeModifier": 1.1, "workerRequirement": {"count": 9, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "人力开采", "description": "工人手工开采银矿，产量较低。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 8, "type": "worker"}}, {"id": "tools", "name": "工具辅助", "description": "使用专业采矿工具提高效率。", "timeModifier": 0.8, "inputModifiers": {"tool": 0.2}, "outputModifiers": {"silver_ingot": 1.3}, "enableByproducts": true, "workerRequirement": {"count": 6, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.6, "workerModifier": 1.6}]}, "goblin_workshop": {"name": "地精工坊", "type": "production", "sprite": "goblin_workshop", "spriteIndex": 27, "recipe": {"input": {"tool": 2, "iron_ingot": 1, "arcane_crystal": 1}, "output": {"goblin_tool": 1}}, "productionInterval": 8000, "cost": {"wood_plank": 40, "iron_ingot": 20, "refined_crystal": 5}, "description": "由地精工匠运营的特殊工坊，生产高效但稀有的地精工具。", "workerRequirement": {"count": 3, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于地精工具生产，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "地精工艺", "description": "地精使用传统工艺制作工具，品质高但速度慢。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}, {"id": "arcane_infusion", "name": "奥术注入", "description": "使用奥术能量注入工具，大幅提高品质和效率。", "timeModifier": 0.7, "inputModifiers": {"tool": 2, "iron_ingot": 1, "arcane_crystal": 2, "mana": 5}, "outputModifiers": {"goblin_tool": 1.5}, "enableByproducts": false, "workerRequirement": {"count": 2, "type": "worker"}}]}, "brick_kiln": {"name": "砖窑", "type": "production", "sprite": "brick_kiln", "spriteIndex": 28, "recipe": {"input": {"clay": 3, "water": 1, "firewood": 1}, "output": {"red_brick": 4}}, "productionInterval": 5000, "cost": {"wood_plank": 20, "stone": 15}, "description": "将粘土与水混合后烧制成红砖，是建筑的重要材料。", "workerRequirement": {"count": 4, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于红砖生产，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 4, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "传统烧制", "description": "使用传统方法烧制红砖，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "improved_kiln", "name": "改良窑炉", "description": "使用改良窑炉提高烧制效率，但需要更多燃料和水。", "timeModifier": 0.7, "inputModifiers": {"clay": 3, "water": 2, "firewood": 1, "charcoal": 1}, "outputModifiers": {"red_brick": 5}, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.6, "workerModifier": 1.5}]}, "glass_workshop": {"name": "玻璃工坊", "type": "production", "sprite": "glass_workshop", "spriteIndex": 29, "recipe": {"input": {"sand": 3, "firewood": 2}, "output": {"glass": 2}}, "productionInterval": 6000, "cost": {"wood_plank": 25, "stone": 20, "red_brick": 15}, "description": "将沙子熔炼成玻璃，用于制作窗户和容器。", "workerRequirement": {"count": 5, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于玻璃生产，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 5, "type": "worker"}}, {"id": "bottles", "name": "玻璃瓶", "description": "同时生产一些玻璃瓶作为副产品。", "resources": {"glass_bottle": 1}, "timeModifier": 1.2, "workerRequirement": {"count": 6, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "传统熔炼", "description": "使用传统方法熔炼玻璃，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 5, "type": "worker"}}, {"id": "improved_furnace", "name": "改良熔炉", "description": "使用改良熔炉提高熔炼效率，但需要更多燃料。", "timeModifier": 0.7, "inputModifiers": {"sand": 3, "charcoal": 2}, "outputModifiers": {"glass": 2.5}, "enableByproducts": true, "workerRequirement": {"count": 4, "type": "worker"}}]}, "porcelain_workshop": {"name": "瓷器工坊", "type": "production", "sprite": "porcelain_workshop", "spriteIndex": 30, "recipe": {"input": {"porcelain_clay": 2, "charcoal": 1}, "output": {"porcelain": 1}}, "productionInterval": 7000, "cost": {"wood_plank": 30, "stone": 25, "red_brick": 20}, "description": "将瓷土烧制成精美的瓷器，是贵族喜爱的奢侈品。", "workerRequirement": {"count": 4, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于瓷器生产，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 4, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "传统工艺", "description": "使用传统工艺制作瓷器，品质高但产量低。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "master_craftsman", "name": "大师工艺", "description": "由瓷器大师主导制作，品质极高但成本更高。", "timeModifier": 1.2, "inputModifiers": {"porcelain_clay": 3, "charcoal": 2}, "outputModifiers": {"porcelain": 1.5}, "enableByproducts": false, "workerRequirement": {"count": 3, "type": "craftsman"}}]}, "pottery_workshop": {"name": "陶器工坊", "type": "production", "sprite": "pottery_workshop", "spriteIndex": 31, "recipe": {"input": {"pottery_clay": 2, "water": 1, "firewood": 1}, "output": {"pottery": 2}}, "productionInterval": 4000, "cost": {"wood_plank": 20, "stone": 15}, "description": "将陶土与水混合后制作成各种实用的陶器，如碗、罐等日用品。", "workerRequirement": {"count": 3, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于陶器生产，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "手工制作", "description": "手工制作陶器，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}, {"id": "wheel_throwing", "name": "轮盘成型", "description": "使用轮盘技术和更多水提高制作效率和品质。", "timeModifier": 0.8, "inputModifiers": {"pottery_clay": 2, "water": 2, "firewood": 1}, "outputModifiers": {"pottery": 1.5}, "enableByproducts": true, "workerRequirement": {"count": 2, "type": "worker"}}]}, "window_workshop": {"name": "窗户工坊", "type": "production", "sprite": "window_workshop", "spriteIndex": 32, "recipe": {"input": {"glass": 2, "wood_plank": 1}, "output": {"glass_window": 1}}, "productionInterval": 5000, "cost": {"wood_plank": 25, "stone": 15, "glass": 10}, "description": "将玻璃和木材制作成窗户，用于高级建筑。", "workerRequirement": {"count": 4, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于窗户生产，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 4, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "手工制作", "description": "手工制作窗户，品质适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "craftsman", "name": "工匠制作", "description": "由专业工匠制作窗户，品质更高但需要更多资源。", "timeModifier": 0.8, "inputModifiers": {"glass": 2.5, "wood_plank": 1.5}, "outputModifiers": {"glass_window": 1.5}, "enableByproducts": true, "workerRequirement": {"count": 2, "type": "craftsman"}}]}, "paper_mill": {"name": "造纸厂", "type": "production", "sprite": "paper_mill", "spriteIndex": 33, "recipe": {"input": {"enchanted_wood": 2, "water": 3}, "output": {"paper": 3}}, "productionInterval": 4000, "cost": {"wood_plank": 30, "stone": 20, "iron_ingot": 5}, "description": "将木材与水混合加工成纸张，用于书籍和文档制作。", "workerRequirement": {"count": 5, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于纸张生产，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 5, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "传统造纸", "description": "使用传统方法制作纸张，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 5, "type": "worker"}}, {"id": "water_powered", "name": "水力造纸", "description": "使用水力驱动造纸机，大幅提高效率，但需要更多水。", "timeModifier": 0.6, "inputModifiers": {"enchanted_wood": 3, "water": 5}, "outputModifiers": {"paper": 4}, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.6, "workerModifier": 1.5}]}, "bookbindery": {"name": "书籍装订所", "type": "production", "sprite": "bookbindery", "spriteIndex": 34, "recipe": {"input": {"paper": 5, "wood_plank": 1}, "output": {"book": 1}}, "productionInterval": 7000, "cost": {"wood_plank": 35, "stone": 20, "glass": 5}, "description": "将纸张装订成书籍，是知识传播的重要场所。", "workerRequirement": {"count": 3, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于书籍生产，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "worker"}}, {"id": "knowledge", "name": "知识积累", "description": "在制作过程中积累知识作为副产品。", "resources": {"knowledge": 0.2}, "timeModifier": 1.2, "workerRequirement": {"count": 4, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "手工装订", "description": "手工装订书籍，品质适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}, {"id": "scholar", "name": "学者编纂", "description": "由学者参与编纂书籍，品质更高但成本更大。", "timeModifier": 1.2, "inputModifiers": {"paper": 6, "wood_plank": 2, "research_point": 1}, "outputModifiers": {"book": 1, "knowledge": 0.5}, "enableByproducts": true, "workerRequirement": {"count": 2, "type": "craftsman"}}]}, "charcoal_kiln": {"name": "木炭窑", "type": "production", "sprite": "charcoal_kiln", "spriteIndex": 35, "recipe": {"input": {"firewood": 3}, "output": {"charcoal": 2}}, "productionInterval": 6000, "cost": {"wood_plank": 15, "stone": 25, "red_brick": 10}, "description": "将木柴烧制成木炭，是高温冶炼的重要燃料。", "workerRequirement": {"count": 3, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于木炭生产，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "传统烧制", "description": "使用传统方法烧制木炭，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}, {"id": "improved_kiln", "name": "改良窑炉", "description": "使用改良窑炉提高烧制效率和产量。", "timeModifier": 0.8, "inputModifiers": {"firewood": 4}, "outputModifiers": {"charcoal": 3}, "enableByproducts": true, "workerRequirement": {"count": 2, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.6, "workerModifier": 1.5}]}, "well": {"name": "水井", "type": "infrastructure", "sprite": "well", "spriteIndex": 36, "recipe": {"input": {}, "output": {"water": 5}}, "productionInterval": 2000, "cost": {"stone": 50}, "description": "从地下抽取清洁的水源，降低居民疾病率30%。", "workerRequirement": {"count": 2, "type": "worker"}, "healthBonus": 0.3, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于取水，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 2, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "人力取水", "description": "工人手动提水，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 2, "type": "worker"}}, {"id": "windmill_pump", "name": "风车水泵", "description": "使用风车驱动水泵，大幅提高取水效率。", "timeModifier": 0.6, "outputModifiers": {"water": 2.0}, "enableByproducts": true, "workerRequirement": {"count": 1, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.6, "workerModifier": 1.5}]}, "iron_mine": {"name": "铁矿", "type": "collector", "sprite": "iron_mine", "spriteIndex": 37, "recipe": {"input": {}, "output": {"iron_ore_raw": 2}}, "productionInterval": 3500, "cost": {"wood_plank": 25, "stone": 20, "tool": 5}, "description": "开采铁原矿，是铁器制造的基础。", "workerRequirement": {"count": 6, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于铁矿开采，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 6, "type": "worker"}}, {"id": "stone", "name": "石料采集", "description": "开采过程中收集石料作为副产品。", "resources": {"stone": 2, "gravel": 1}, "timeModifier": 1.1, "workerRequirement": {"count": 7, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "人力开采", "description": "工人手工开采铁矿，产量较低。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 6, "type": "worker"}}, {"id": "tools", "name": "工具辅助", "description": "使用专业采矿工具提高效率。", "timeModifier": 0.8, "inputModifiers": {"tool": 0.2}, "outputModifiers": {"iron_ore_raw": 1.5}, "enableByproducts": true, "workerRequirement": {"count": 4, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.6, "workerModifier": 1.6}]}, "ore_processing": {"name": "矿石处理厂", "type": "production", "sprite": "ore_processing", "spriteIndex": 38, "recipe": {"input": {"iron_ore_raw": 3, "water": 1}, "output": {"iron_ore": 2}}, "productionInterval": 3000, "cost": {"wood_plank": 20, "stone": 15, "iron_ingot": 5}, "description": "将原矿石洗选加工成纯净矿石，提高冶炼效率。", "workerRequirement": {"count": 4, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于矿石处理，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "gravel", "name": "碎石收集", "description": "处理过程中收集碎石作为副产品。", "resources": {"gravel": 2}, "timeModifier": 1.1, "workerRequirement": {"count": 5, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "手工筛选", "description": "工人手工筛选矿石，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "water_powered", "name": "水力筛选", "description": "使用水力设备提高筛选效率，但需要更多水。", "timeModifier": 0.7, "inputModifiers": {"iron_ore_raw": 3, "water": 2}, "outputModifiers": {"iron_ore": 2.5}, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.6, "workerModifier": 1.5}]}, "copper_mine": {"name": "铜矿", "type": "collector", "sprite": "copper_mine", "spriteIndex": 39, "recipe": {"input": {}, "output": {"copper_ore_raw": 2}}, "productionInterval": 3000, "cost": {"wood_plank": 20, "stone": 15, "tool": 3}, "description": "开采铜原矿，是铜器制造的基础。", "workerRequirement": {"count": 5, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于铜矿开采，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 5, "type": "worker"}}, {"id": "stone", "name": "石料采集", "description": "开采过程中收集石料作为副产品。", "resources": {"stone": 2, "gravel": 1}, "timeModifier": 1.1, "workerRequirement": {"count": 6, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "人力开采", "description": "工人手工开采铜矿，产量较低。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 5, "type": "worker"}}, {"id": "tools", "name": "工具辅助", "description": "使用专业采矿工具提高效率。", "timeModifier": 0.8, "inputModifiers": {"tool": 0.2}, "outputModifiers": {"copper_ore_raw": 1.5}, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.6, "workerModifier": 1.6}]}, "copper_processing": {"name": "铜矿处理厂", "type": "production", "sprite": "copper_processing", "spriteIndex": 40, "recipe": {"input": {"copper_ore_raw": 3, "water": 1}, "output": {"copper_ore": 2}}, "productionInterval": 2800, "cost": {"wood_plank": 18, "stone": 12, "iron_ingot": 3}, "description": "将铜原矿洗选加工成纯净铜矿石，提高冶炼效率。", "workerRequirement": {"count": 4, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于铜矿处理，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "gravel", "name": "碎石收集", "description": "处理过程中收集碎石作为副产品。", "resources": {"gravel": 2}, "timeModifier": 1.1, "workerRequirement": {"count": 5, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "手工筛选", "description": "工人手工筛选铜矿石，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "water_powered", "name": "水力筛选", "description": "使用水力设备提高筛选效率，但需要更多水。", "timeModifier": 0.7, "inputModifiers": {"copper_ore_raw": 3, "water": 2}, "outputModifiers": {"copper_ore": 2.5}, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.6, "workerModifier": 1.5}]}, "copper_smith": {"name": "铜匠铺", "type": "production", "sprite": "copper_smith", "spriteIndex": 41, "recipe": {"input": {"copper_ore": 2, "firewood": 1}, "output": {"copper_ingot": 1}}, "productionInterval": 3500, "cost": {"wood_plank": 20, "stone": 15, "red_brick": 10}, "description": "冶炼铜矿石并制作各种铜制品，是早期工业的重要组成部分。", "workerRequirement": {"count": 3, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于铜冶炼，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "worker"}}, {"id": "tools", "name": "铜工具制作", "description": "同时制作铜制工具作为副产品。", "resources": {"tool": 0.3}, "timeModifier": 1.2, "workerRequirement": {"count": 4, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "传统锻造", "description": "使用传统锻造方法，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}, {"id": "improved_furnace", "name": "改良熔炉", "description": "使用改良熔炉提高冶炼效率，但需要更多燃料。", "timeModifier": 0.7, "inputModifiers": {"copper_ore": 2, "firewood": 1, "charcoal": 1}, "outputModifiers": {"copper_ingot": 1.5}, "enableByproducts": true, "workerRequirement": {"count": 2, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.6, "workerModifier": 1.5}]}, "magic_crystal_mine": {"name": "魔晶矿", "type": "collector", "sprite": "magic_crystal_mine", "spriteIndex": 42, "recipe": {"input": {}, "output": {"magic_crystal_ore": 1}}, "productionInterval": 5000, "cost": {"wood_plank": 30, "stone": 25, "iron_ingot": 10}, "description": "开采稀有的魔晶矿石，是魔法道具和高级装备的重要原料。", "workerRequirement": {"count": 7, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于魔晶矿开采，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 7, "type": "worker"}}, {"id": "magic_stone", "name": "魔石采集", "description": "开采过程中收集魔石作为副产品。", "resources": {"magic_stone_ore": 0.5}, "timeModifier": 1.2, "workerRequirement": {"count": 8, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "人力开采", "description": "工人手工开采魔晶矿，产量较低。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 7, "type": "worker"}}, {"id": "magic_tools", "name": "魔法工具", "description": "使用魔法工具提高开采效率。", "timeModifier": 0.7, "inputModifiers": {"mana": 2}, "outputModifiers": {"magic_crystal_ore": 1.5}, "enableByproducts": true, "workerRequirement": {"count": 5, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "three_shifts", "name": "三班倒", "description": "三班倒工作制，生产速度提高但需要更多工人。", "timeModifier": 0.6, "workerModifier": 1.6}]}, "magic_crystal_workshop": {"name": "魔晶工坊", "type": "production", "sprite": "magic_crystal_workshop", "spriteIndex": 43, "recipe": {"input": {"magic_crystal_ore": 2, "mana": 1}, "output": {"magic_crystal": 1}}, "productionInterval": 6000, "cost": {"wood_plank": 35, "stone": 30, "iron_ingot": 15, "glass": 10}, "description": "将魔晶矿石提纯为魔晶石，用于高级魔法道具的制作。", "workerRequirement": {"count": 4, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于魔晶石提纯，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "medium_crystal", "name": "中品魔晶石", "description": "同时生产少量中品魔晶石作为副产品。", "resources": {"medium_magic_crystal": 0.2}, "timeModifier": 1.3, "workerRequirement": {"count": 5, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "基础提纯", "description": "使用基础魔法提纯魔晶石，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "advanced_magic", "name": "高级魔法", "description": "使用高级魔法提高提纯效率和品质。", "timeModifier": 0.8, "inputModifiers": {"magic_crystal_ore": 2, "mana": 3}, "outputModifiers": {"magic_crystal": 1.5}, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "intensive", "name": "密集工作", "description": "密集工作模式，生产速度提高但需要更多工人。", "timeModifier": 0.7, "workerModifier": 1.4}]}, "leather_workshop": {"name": "皮革工坊", "type": "production", "sprite": "leather_workshop", "spriteIndex": 44, "recipe": {"input": {"animal_hide": 2, "water": 1}, "output": {"leather": 1}}, "productionInterval": 3500, "cost": {"wood_plank": 20, "stone": 15}, "description": "将兽皮加工成皮革，用于制作衣物、盔甲和其他物品。", "workerRequirement": {"count": 3, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于皮革生产，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "传统鞣制", "description": "使用传统方法鞣制皮革，产量适中。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}, {"id": "improved_tanning", "name": "改良鞣制", "description": "使用改良鞣制方法提高效率和品质。", "timeModifier": 0.8, "inputModifiers": {"animal_hide": 2, "water": 2}, "outputModifiers": {"leather": 1.5}, "enableByproducts": true, "workerRequirement": {"count": 2, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "intensive", "name": "密集工作", "description": "密集工作模式，生产速度提高但需要更多工人。", "timeModifier": 0.7, "workerModifier": 1.4}]}, "magic_beast_workshop": {"name": "魔兽皮工坊", "type": "production", "sprite": "magic_beast_workshop", "spriteIndex": 45, "recipe": {"input": {"magic_beast_hide": 1, "water": 1, "mana": 1}, "output": {"leather": 3}}, "productionInterval": 5000, "cost": {"wood_plank": 30, "stone": 25, "iron_ingot": 10}, "description": "将稀有的魔兽皮加工成高品质皮革，产量高但需要特殊材料。", "workerRequirement": {"count": 4, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于高品质皮革生产，不生产副产品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 4, "type": "worker"}}], "productionMethods": [{"id": "manual", "name": "魔法鞣制", "description": "使用魔法辅助鞣制魔兽皮，产量高。", "timeModifier": 1.0, "enableByproducts": true, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "advanced_magic", "name": "高级魔法", "description": "使用高级魔法提高鞣制效率和品质。", "timeModifier": 0.7, "inputModifiers": {"magic_beast_hide": 1, "water": 1, "mana": 2}, "outputModifiers": {"leather": 4}, "enableByproducts": true, "workerRequirement": {"count": 3, "type": "worker"}}], "workModes": [{"id": "normal", "name": "正常工作", "description": "正常工作时间，标准产出。", "timeModifier": 1.0, "workerModifier": 1.0}, {"id": "intensive", "name": "密集工作", "description": "密集工作模式，生产速度提高但需要更多工人。", "timeModifier": 0.7, "workerModifier": 1.4}]}, "weapon_shop": {"name": "武器店", "type": "adventurer_support", "sprite": "weapon_shop", "spriteIndex": 53, "recipe": {"input": {"iron": 2, "wood": 1}, "output": {"weapon": 1}}, "productionInterval": 7200000, "cost": {"wood_plank": 40, "stone": 25, "iron": 15, "gold": 200}, "description": "從市場收購武器材料，精製成高品質武器出售給冒險者。", "workerRequirement": {"count": 3, "type": "craftsman"}, "specialFunction": "weapon_crafting", "byproductTypes": [{"id": "basic_weapons", "name": "基礎武器", "description": "製作基本品質的武器。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "craftsman"}, "specialEffect": "basic_weapon_quality"}, {"id": "masterwork_weapons", "name": "大師級武器", "description": "製作高品質的大師級武器。", "resources": {"mana": 1}, "timeModifier": 1.5, "workerRequirement": {"count": 4, "type": "master_craftsman"}, "specialEffect": "masterwork_weapon_quality"}]}, "mine": {"name": "铁矿", "type": "collector", "sprite": "iron_mine", "spriteIndex": 37, "recipe": {"input": {}, "output": {"iron_ore": 20}}, "productionInterval": 8640, "cost": {"wood": 50, "stone": 30}, "description": "10矿工/日产出20单位铁矿，日薪4份小麦，每5天消耗1把铁镐。", "workerRequirement": {"count": 10, "type": "miner"}, "dailyWage": {"wheat": 4}, "toolConsumption": {"iron_pickaxe": 0.2}}, "lumber_camp": {"name": "伐木场", "type": "collector", "sprite": "lumber_mill", "spriteIndex": 24, "recipe": {"input": {}, "output": {"wood": 50}}, "productionInterval": 8640, "cost": {"wood": 20, "stone": 10}, "description": "5伐木工/日产出50单位木材，日薪3.5份小麦，每3天消耗1把斧头。", "workerRequirement": {"count": 5, "type": "lumberjack"}, "dailyWage": {"wheat": 3.5}, "toolConsumption": {"axe": 0.33}}, "hunters_lodge": {"name": "猎人小屋", "type": "collector", "sprite": "hunters_lodge", "spriteIndex": 20, "recipe": {"input": {"dog_food": 2}, "output": {"meat": 5}}, "productionInterval": 8640, "cost": {"wood": 30, "stone": 15}, "description": "3猎人/日产出5份肉类，日薪4份小麦，需2只猎犬。", "workerRequirement": {"count": 3, "type": "hunter"}, "dailyWage": {"wheat": 4}, "animalRequirement": {"hunting_dog": 2}}, "fishing_dock": {"name": "渔场", "type": "collector", "sprite": "fishing_dock", "spriteIndex": 19, "recipe": {"input": {}, "output": {"fish": 15}}, "productionInterval": 8640, "cost": {"wood": 40, "stone": 20}, "description": "8渔民/日产出15份鱼类，日薪3.5份小麦，渔船每月消耗10木材。", "workerRequirement": {"count": 8, "type": "fisherman"}, "dailyWage": {"wheat": 3.5}, "equipmentMaintenance": {"wood": 0.33}}, "mill": {"name": "磨坊", "type": "production", "sprite": "lumber_mill", "spriteIndex": 24, "recipe": {"input": {"wheat": 10}, "output": {"flour": 1}}, "productionInterval": 8640, "cost": {"wood": 100, "stone": 50}, "description": "2工人/日加工100小麦，10小麦=1份面粉，工人日薪3.5小麦。", "workerRequirement": {"count": 2, "type": "worker"}, "dailyWage": {"wheat": 3.5}}, "bakery": {"name": "面包房", "type": "production", "sprite": "bakery", "spriteIndex": 23, "recipe": {"input": {"flour": 1, "yeast": 0.5}, "output": {"bread": 2}}, "productionInterval": 8640, "cost": {"wood": 80, "stone": 60, "red_brick": 40}, "description": "1面粉+0.5酵母=2个面包，面包售价5铜币，可满足2天生存需求。", "workerRequirement": {"count": 1, "type": "baker"}, "dailyWage": {"wheat": 4}}, "smithy": {"name": "铁匠铺", "type": "production", "sprite": "blacksmith", "spriteIndex": 25, "recipe": {"input": {"iron_ore": 5, "wood": 2}, "output": {"iron_pickaxe": 1}}, "productionInterval": 8640, "cost": {"wood": 120, "stone": 80, "red_brick": 60}, "description": "5铁矿+2木材=1把铁镐，铁镐售价20铜币，铁匠日薪5小麦。", "workerRequirement": {"count": 1, "type": "blacksmith"}, "dailyWage": {"wheat": 5}}, "carpentry_shop": {"name": "木匠铺", "type": "production", "sprite": "lumber_mill", "spriteIndex": 24, "recipe": {"input": {"wood": 10}, "output": {"plow": 1}}, "productionInterval": 8640, "cost": {"wood": 80, "stone": 40}, "description": "10木材=1张犁，犁售价50铜币，可使农田年产量增加200份小麦。", "workerRequirement": {"count": 1, "type": "carpenter"}, "dailyWage": {"wheat": 4}}, "granary": {"name": "谷仓", "type": "infrastructure", "sprite": "storage_vault", "spriteIndex": 17, "recipe": {"input": {}, "output": {}}, "productionInterval": 0, "cost": {"wood": 200, "stone": 100}, "description": "容量500，可囤货平抑价格，防止价格波动过大。", "storageCapacity": 500, "priceStabilization": true}, "market": {"name": "市场", "type": "infrastructure", "sprite": "housing_district", "spriteIndex": 12, "recipe": {"input": {}, "output": {}}, "productionInterval": 0, "cost": {"wood": 200}, "description": "提升交易效率50%，缩小价格波动，促进商业发展。", "tradeEfficiencyBonus": 0.5}, "castle": {"name": "城堡", "type": "infrastructure", "sprite": "wizard_tower", "spriteIndex": 10, "recipe": {"input": {}, "output": {}}, "productionInterval": 0, "cost": {"stone": 1000, "wood": 500}, "description": "解锁征兵功能，但增加居民税收负担20%。", "militaryUnlock": true, "taxBurdenIncrease": 0.2}, "hospital": {"name": "医院", "type": "infrastructure", "sprite": "research_lab", "spriteIndex": 16, "recipe": {"input": {}, "output": {}}, "productionInterval": 0, "cost": {"silver_coin": 200}, "description": "治疗瘟疫，降低人口死亡率，提高城市健康水平。", "healthBonus": 0.4, "plagueResistance": true}, "_comment_resource_value_buildings": "========== 资源值建筑 ==========", "tavern": {"name": "酒館", "type": "adventurer_support", "sprite": "tavern", "spriteIndex": 52, "recipe": {"input": {"ale": 1, "food": 1}, "output": {}}, "productionInterval": 3600000, "cost": {"wood_plank": 25, "stone": 15, "iron": 5}, "description": "冒險者聚集的地方，可以收集情報、接受黑市任務和招募新成員。", "workerRequirement": {"count": 2, "type": "worker"}, "specialFunction": "adventurer_tavern", "byproductTypes": [{"id": "information_hub", "name": "情報中心", "description": "收集各種情報，提高任務成功率。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 2, "type": "worker"}, "specialEffect": "information_bonus"}, {"id": "black_market", "name": "黑市交易", "description": "提供非法任務和黑市交易機會。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "rogue"}, "specialEffect": "black_market_access"}]}, "labor_hall": {"name": "人力馆", "type": "resource_value", "sprite": "labor_hall", "spriteIndex": 51, "recipe": {"input": {"bread": 1}, "output": {}}, "productionInterval": 0, "cost": {"wood_plank": 25, "stone": 15}, "description": "组织和调配劳动力，每季度增加运力资源值100点。", "workerRequirement": {"count": 2, "type": "worker"}, "resourceValueBonus": {"transport": 100}, "dailyConsumption": {"bread": 1}, "byproductTypes": [{"id": "none", "name": "基础运力", "description": "提供基础运力支持。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 2, "type": "worker"}}, {"id": "cart_upgrade", "name": "人力车升级", "description": "配备人力车，运力资源值增加50%。", "resources": {}, "timeModifier": 1.0, "resourceValueModifier": {"transport": 1.5}, "workerRequirement": {"count": 3, "type": "worker"}, "cost": {"wood_plank": 10, "iron_ingot": 2}}]}, "guard_post": {"name": "守卫所", "type": "resource_value", "sprite": "guard_post", "spriteIndex": 52, "recipe": {"input": {"bread": 3, "iron_ingot": 1}, "output": {}}, "productionInterval": 0, "cost": {"stone": 40, "iron_ingot": 15, "wood_plank": 10}, "description": "维护城市治安，每季度增加安保力资源值75点。", "workerRequirement": {"count": 4, "type": "worker"}, "resourceValueBonus": {"security": 75}, "dailyConsumption": {"bread": 3, "iron_ingot": 1}}, "textile_workshop": {"name": "纺织工坊", "type": "production", "sprite": "textile_workshop", "spriteIndex": 53, "recipe": {"input": {"linen": 2}, "output": {"cloth": 3}}, "productionInterval": 4000, "cost": {"wood_plank": 20, "stone": 10}, "description": "将亚麻等原料纺织成布料，为居民提供基本衣物材料。", "workerRequirement": {"count": 3, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于纺织布料。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "worker"}}, {"id": "dye_cloth", "name": "染色布料", "description": "生产染色布料，品质更高。", "resources": {"fine_clothing": 1}, "timeModifier": 1.2, "workerRequirement": {"count": 4, "type": "worker"}}]}, "simple_kitchen": {"name": "简易厨房", "type": "production", "sprite": "simple_kitchen", "spriteIndex": 54, "recipe": {"input": {"wheat": 3, "meat": 1, "water": 1}, "output": {"simple_meal": 4}}, "productionInterval": 2500, "cost": {"stone": 15, "iron_ingot": 5, "wood_plank": 10}, "description": "为底层居民制作简单但营养的餐食。", "workerRequirement": {"count": 2, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于制作简单餐食。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 2, "type": "worker"}}, {"id": "soup_kitchen", "name": "汤厨模式", "description": "制作更多汤类餐食，产量增加但品质略降。", "resources": {}, "timeModifier": 0.8, "outputModifiers": {"simple_meal": 1.5}, "workerRequirement": {"count": 3, "type": "worker"}}]}, "linen_farm": {"name": "亚麻农场", "type": "production", "sprite": "linen_farm", "spriteIndex": 55, "recipe": {"input": {"water": 2}, "output": {"linen": 2}}, "productionInterval": 6000, "cost": {"wood_plank": 15, "stone": 5}, "description": "种植亚麻，为纺织业提供原料。", "workerRequirement": {"count": 4, "type": "worker"}, "byproductTypes": [{"id": "none", "name": "无副产品", "description": "专注于种植亚麻。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 4, "type": "worker"}}, {"id": "seed_collection", "name": "种子收集", "description": "收集亚麻种子用于扩大种植。", "resources": {"linen": 1}, "timeModifier": 1.1, "workerRequirement": {"count": 5, "type": "worker"}}]}, "adventurer_guild": {"name": "冒險者公會", "type": "special", "sprite": "adventurer_guild", "spriteIndex": 50, "recipe": {"input": {}, "output": {}}, "productionInterval": 0, "cost": {"wood_plank": 50, "stone": 10, "gold": 500}, "description": "冒險者聚集的地方，可以管理冒險者團隊並發布任務。建造後啟動冒險者管理系統。", "workerRequirement": {"count": 2, "type": "worker"}, "specialFunction": "adventurer_management", "byproductTypes": [{"id": "none", "name": "基本運營", "description": "提供基本的冒險者管理服務。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 2, "type": "worker"}}, {"id": "recruitment", "name": "積極招募", "description": "主動招募新的冒險者團隊，增加團隊數量。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "worker"}, "specialEffect": "increased_recruitment"}, {"id": "training", "name": "訓練支援", "description": "為冒險者提供訓練，提高任務成功率。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 4, "type": "craftsman"}, "specialEffect": "training_bonus"}]}, "inn": {"name": "旅館", "type": "adventurer_support", "sprite": "inn", "spriteIndex": 51, "recipe": {"input": {"food": 2, "water": 1}, "output": {}}, "productionInterval": 3600000, "cost": {"wood_plank": 30, "stone": 20, "cloth": 10}, "description": "為冒險者提供休息場所，可以治療傷勢並提供臨時增益效果。", "workerRequirement": {"count": 2, "type": "worker"}, "specialFunction": "adventurer_healing", "byproductTypes": [{"id": "basic_rest", "name": "基本休息", "description": "提供基本的治療和休息服務。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 2, "type": "worker"}, "specialEffect": "basic_healing"}, {"id": "luxury_service", "name": "豪華服務", "description": "提供高級服務，額外增加戰鬥力加成。", "resources": {"wine": 1}, "timeModifier": 1.2, "workerRequirement": {"count": 3, "type": "craftsman"}, "specialEffect": "luxury_buff"}]}, "armor_shop": {"name": "防具店", "type": "adventurer_support", "sprite": "armor_shop", "spriteIndex": 54, "recipe": {"input": {"iron": 3, "leather": 1}, "output": {"armor": 1}}, "productionInterval": 7200000, "cost": {"wood_plank": 35, "stone": 20, "iron": 20, "gold": 180}, "description": "從市場收購防具材料，精製成高品質防具出售給冒險者。", "workerRequirement": {"count": 3, "type": "craftsman"}, "specialFunction": "armor_crafting", "byproductTypes": [{"id": "basic_armor", "name": "基礎防具", "description": "製作基本品質的防具。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "craftsman"}, "specialEffect": "basic_armor_quality"}, {"id": "enchanted_armor", "name": "附魔防具", "description": "製作附魔的高品質防具。", "resources": {"mana": 1}, "timeModifier": 1.5, "workerRequirement": {"count": 4, "type": "master_craftsman"}, "specialEffect": "enchanted_armor_quality"}]}, "item_shop": {"name": "道具店", "type": "adventurer_support", "sprite": "item_shop", "spriteIndex": 55, "recipe": {"input": {"herbs": 2, "water": 1, "food": 1}, "output": {"adventurer_supplies": 1}}, "productionInterval": 3600000, "cost": {"wood_plank": 25, "stone": 15, "gold": 150}, "description": "從市場收購物資，精製成冒險者專用的補給品。", "workerRequirement": {"count": 2, "type": "worker"}, "specialFunction": "supply_crafting", "byproductTypes": [{"id": "basic_supplies", "name": "基礎補給", "description": "製作基本的冒險補給品。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 2, "type": "worker"}, "specialEffect": "basic_supply_quality"}, {"id": "premium_supplies", "name": "高級補給", "description": "製作高品質的冒險補給品，持續時間更長。", "resources": {"mana": 1}, "timeModifier": 1.3, "workerRequirement": {"count": 3, "type": "craftsman"}, "specialEffect": "premium_supply_quality"}]}, "church": {"name": "教會", "type": "adventurer_support", "sprite": "church", "spriteIndex": 56, "recipe": {"input": {"mana": 3, "holy_water": 1}, "output": {}}, "productionInterval": 0, "cost": {"stone": 50, "wood_plank": 30, "gold": 300, "holy_relic": 1}, "description": "神聖的場所，可以復活陣亡的冒險者並提供神聖祝福。", "workerRequirement": {"count": 2, "type": "cleric"}, "specialFunction": "adventurer_resurrection", "byproductTypes": [{"id": "basic_healing", "name": "基礎治療", "description": "提供基本的復活和治療服務。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 2, "type": "cleric"}, "specialEffect": "basic_resurrection"}, {"id": "divine_blessing", "name": "神聖祝福", "description": "提供強力的復活服務和永久祝福效果。", "resources": {"holy_water": 1}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "high_priest"}, "specialEffect": "divine_blessing"}]}, "training_camp": {"name": "訓練營", "type": "adventurer_support", "sprite": "training_camp", "spriteIndex": 57, "recipe": {"input": {"food": 2, "weapon": 1}, "output": {}}, "productionInterval": 0, "cost": {"wood_plank": 45, "stone": 30, "iron": 25, "gold": 250}, "description": "專業的訓練場所，可以提升冒險者的各項能力。", "workerRequirement": {"count": 3, "type": "veteran"}, "specialFunction": "adventurer_training", "byproductTypes": [{"id": "basic_training", "name": "基礎訓練", "description": "提供基本的戰鬥技能訓練。", "resources": {}, "timeModifier": 1.0, "workerRequirement": {"count": 3, "type": "veteran"}, "specialEffect": "basic_training"}, {"id": "advanced_training", "name": "高級訓練", "description": "提供專業的高級戰鬥訓練，大幅提升能力。", "resources": {"weapon": 1, "armor": 1}, "timeModifier": 1.5, "workerRequirement": {"count": 4, "type": "master_trainer"}, "specialEffect": "advanced_training"}, {"id": "specialized_training", "name": "專精訓練", "description": "針對不同職業提供專門的技能訓練。", "resources": {"mana": 1, "weapon": 1}, "timeModifier": 2.0, "workerRequirement": {"count": 5, "type": "grandmaster"}, "specialEffect": "specialized_training"}]}}